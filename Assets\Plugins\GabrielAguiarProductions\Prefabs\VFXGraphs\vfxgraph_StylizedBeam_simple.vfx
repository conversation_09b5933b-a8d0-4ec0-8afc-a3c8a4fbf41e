%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &114340500867371532
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d01270efd3285ea4a9d6c555cb0a8027, type: 3}
  m_Name: VFXUI
  m_EditorClassIdentifier: 
  groupInfos: []
  stickyNoteInfos: []
  categories: []
  uiBounds:
    serializedVersion: 2
    x: 1488
    y: -183
    width: 3477
    height: 2116
--- !u!114 &114350483966674976
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7d4c867f6b72b714dbb5fd1780afe208, type: 3}
  m_Name: vfxgraph_StylizedBeam_simple
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661614599}
  - {fileID: 8926484042661614612}
  - {fileID: 8926484042661614714}
  - {fileID: 8926484042661614719}
  - {fileID: 8926484042661614741}
  - {fileID: 8926484042661614743}
  - {fileID: 8926484042661614877}
  - {fileID: 8926484042661614918}
  - {fileID: 8926484042661614922}
  - {fileID: 8926484042661614951}
  - {fileID: 8926484042661614953}
  - {fileID: 8926484042661614963}
  - {fileID: 8926484042661614967}
  - {fileID: 8926484042661615003}
  - {fileID: 8926484042661615007}
  - {fileID: 8926484042661615036}
  - {fileID: 8926484042661615038}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_UIInfos: {fileID: 114340500867371532}
  m_ParameterInfo:
  - name: Duration
    path: Duration
    tooltip: 
    sheetType: m_Float
    realType: Single
    defaultValue:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 3
    min: -Infinity
    max: Infinity
    enumValues: []
    descendantCount: 0
  m_ImportDependencies: []
  m_GraphVersion: 12
  m_ResourceVersion: 1
  m_SubgraphDependencies: []
  m_CategoryPath: 
--- !u!2058629511 &8926484042661614527
VisualEffectResource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: vfxgraph_StylizedBeam_simple
  m_Graph: {fileID: 114350483966674976}
  m_Infos:
    m_RendererSettings:
      motionVectorGenerationMode: 0
      shadowCastingMode: 0
      receiveShadows: 0
      reflectionProbeUsage: 0
      lightProbeUsage: 0
    m_CullingFlags: 3
    m_UpdateMode: 0
    m_PreWarmDeltaTime: 0.05
    m_PreWarmStepCount: 0
    m_InitialEventName: OnPlay
    m_InstancingMode: -1
    m_InstancingCapacity: 64
--- !u!114 &8926484042661614599
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e837ba02e1cb47d4394b6c186d164156, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 114350483966674976}
  m_Children:
  - {fileID: 8926484042661614614}
  - {fileID: 8926484042661614604}
  - {fileID: 8926484042661614620}
  - {fileID: 8926484042661614625}
  m_UIPosition: {x: 1488, y: 697}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614629}
  - {fileID: 8926484042661614634}
  - {fileID: 8926484042661614635}
  - {fileID: 8926484042661614636}
  - {fileID: 8926484042661614640}
  - {fileID: 8926484042661614709}
  - {fileID: 8926484042661614712}
  - {fileID: 8926484042661614713}
  - {fileID: 8926484042661614600}
  - {fileID: 8926484042661614601}
  m_OutputSlots: []
  m_Label: BEAM CORE
  m_Data: {fileID: 8926484042661614733}
  m_InputFlowSlot:
  - link:
    - context: {fileID: 8926484042661614741}
      slotIndex: 0
  m_OutputFlowSlot:
  - link: []
  blendMode: 0
  cullMode: 3
  zWriteMode: 0
  zTestMode: 0
  useAlphaClipping: 0
  generateMotionVector: 0
  excludeFromTUAndAA: 0
  sortingPriority: 0
  m_SubOutputs: []
  colorMapping: 0
  uvMode: 0
  flipbookLayout: 0
  useSoftParticle: 0
  vfxSystemSortPriority: 0
  sort: 0
  sortMode: 0
  revertSorting: 0
  indirectDraw: 0
  computeCulling: 0
  frustumCulling: 0
  castShadows: 0
  useExposureWeight: 0
  needsOwnSort: 0
  shaderGraph: {fileID: -5475051401550479605, guid: 78f6abf4cc407f44a9c97b1e8b02d0ed,
    type: 3}
  materialSettings:
    m_PropertyNames: []
    m_PropertyValues: []
  MeshCount: 1
  lod: 0
--- !u!114 &8926484042661614600
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b47b8679b468b7347a00cdd50589bc9f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614600}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614599}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Mesh, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"obj":{"fileID":-591271329399093883,"guid":"9c4c330e4318eca40ab5c1f96494646b","type":3}}'
    m_Space: 2147483647
  m_Property:
    name: mesh
    m_serializedType:
      m_SerializableType: UnityEngine.Mesh, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614601
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c52d920e7fff73b498050a6b3c4404ca, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614601}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614599}
    m_Value:
      m_Type:
        m_SerializableType: System.UInt32, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 4294967295
    m_Space: 2147483647
  m_Property:
    name: subMeshMask
    m_serializedType:
      m_SerializableType: System.UInt32, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614604
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a971fa2e110a0ac42ac1d8dae408704b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614599}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614605}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615059}
  attribute: size
  Composition: 0
  Source: 0
  Random: 0
  channels: 6
--- !u!114 &8926484042661614605
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614605}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614604}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 3
    m_Space: 2147483647
  m_Property:
    name: Size
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614612
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 330e0fca1717dde4aaa144f48232aa64, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 114350483966674976}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots: []
  m_OutputSlots:
  - {fileID: 8926484042661614613}
  m_ExposedName: Duration
  m_Exposed: 1
  m_Order: 0
  m_Category: 
  m_Min:
    m_Type:
      m_SerializableType: 
    m_SerializableObject: 
  m_Max:
    m_Type:
      m_SerializableType: 
    m_SerializableObject: 
  m_IsOutput: 0
  m_EnumValues: []
  m_ValueFilter: 0
  m_Tooltip: 
  m_Nodes:
  - m_Id: 2
    linkedSlots:
    - outputSlot: {fileID: 8926484042661614613}
      inputSlot: {fileID: 8926484042661614735}
    position: {x: 1879, y: 301}
    expandedSlots: []
    expanded: 0
  - m_Id: 5
    linkedSlots:
    - outputSlot: {fileID: 8926484042661614613}
      inputSlot: {fileID: 8926484042661614964}
    position: {x: 3034, y: -80}
    expandedSlots: []
    expanded: 0
  - m_Id: 6
    linkedSlots:
    - outputSlot: {fileID: 8926484042661614613}
      inputSlot: {fileID: 8926484042661614968}
    position: {x: 2899, y: 26}
    expandedSlots: []
    expanded: 0
  - m_Id: 7
    linkedSlots:
    - outputSlot: {fileID: 8926484042661614613}
      inputSlot: {fileID: 8926484042661615051}
    position: {x: 4384.5186, y: 351.06787}
    expandedSlots: []
    expanded: 0
--- !u!114 &8926484042661614613
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614613}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614612}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 3
    m_Space: 2147483647
  m_Property:
    name: o
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 1
  m_LinkedSlots:
  - {fileID: 8926484042661614735}
  - {fileID: 8926484042661614964}
  - {fileID: 8926484042661614968}
  - {fileID: 8926484042661615051}
--- !u!114 &8926484042661614614
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d16c6aeaef944094b9a1633041804207, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614599}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614615}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615058}
  mode: 5
  axes: 4
--- !u!114 &8926484042661614615
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e8f2b4a846fd4c14a893cde576ad172b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661614616}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614615}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614614}
    m_Value:
      m_Type:
        m_SerializableType: UnityEditor.VFX.DirectionType, Unity.VisualEffectGraph.Editor,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"direction":{"x":0.0,"y":0.0,"z":1.0}}'
    m_Space: 0
  m_Property:
    name: Up
    m_serializedType:
      m_SerializableType: UnityEditor.VFX.DirectionType, Unity.VisualEffectGraph.Editor,
        Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614616
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac39bd03fca81b849929b9c966f1836a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614615}
  m_Children:
  - {fileID: 8926484042661614617}
  - {fileID: 8926484042661614618}
  - {fileID: 8926484042661614619}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614615}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: direction
    m_serializedType:
      m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614617
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614616}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614615}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614618
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614616}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614615}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614619
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614616}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614615}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: z
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614620
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a971fa2e110a0ac42ac1d8dae408704b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614599}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614621}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615060}
  attribute: scale
  Composition: 0
  Source: 0
  Random: 0
  channels: 6
--- !u!114 &8926484042661614621
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac39bd03fca81b849929b9c966f1836a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661614622}
  - {fileID: 8926484042661614623}
  - {fileID: 8926484042661614624}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614621}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614620}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"x":0.25,"y":2.0,"z":1.0}'
    m_Space: 2147483647
  m_Property:
    name: Scale
    m_serializedType:
      m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614622
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614621}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614621}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614623
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614621}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614621}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614624
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614621}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614621}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: z
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614625
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 01ec2c1930009b04ea08905b47262415, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614599}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614626}
  - {fileID: 8926484042661614627}
  - {fileID: 8926484042661614628}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615061}
  attribute: scale
  Composition: 2
  AlphaComposition: 0
  SampleMode: 0
  Mode: 1
  ColorMode: 3
  channels: 6
--- !u!114 &8926484042661614626
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c117b74c5c58db542bffe25c78fe92db, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614626}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614625}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.AnimationCurve, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"frames":[{"time":-0.012134552001953125,"value":0.0,"inTangent":6.539741516113281,"outTangent":6.539741516113281,"tangentMode":0,"leftTangentMode":1,"rightTangentMode":1,"broken":false},{"time":0.14077669382095338,"value":1.0,"inTangent":0.570044755935669,"outTangent":0.570044755935669,"tangentMode":0,"leftTangentMode":0,"rightTangentMode":0,"broken":false},{"time":0.8952546715736389,"value":1.0125837326049805,"inTangent":-0.20825791358947755,"outTangent":-0.20825791358947755,"tangentMode":0,"leftTangentMode":0,"rightTangentMode":0,"broken":false},{"time":0.995147705078125,"value":-0.005649566650390625,"inTangent":-10.193236351013184,"outTangent":-10.193236351013184,"tangentMode":0,"leftTangentMode":1,"rightTangentMode":1,"broken":false}],"preWrapMode":8,"postWrapMode":8,"version":1}'
    m_Space: 2147483647
  m_Property:
    name: Scale_x
    m_serializedType:
      m_SerializableType: UnityEngine.AnimationCurve, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614627
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c117b74c5c58db542bffe25c78fe92db, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614627}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614625}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.AnimationCurve, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"frames":[{"time":0.00191497802734375,"value":0.12585067749023438,"inTangent":7.628503799438477,"outTangent":7.628503799438477,"tangentMode":0,"leftTangentMode":1,"rightTangentMode":1,"broken":false},{"time":0.11650485545396805,"value":1.0,"inTangent":-0.10580432415008545,"outTangent":-0.10580432415008545,"tangentMode":0,"leftTangentMode":0,"rightTangentMode":0,"broken":false},{"time":1.0,"value":1.0,"inTangent":0.0,"outTangent":0.0,"tangentMode":0,"leftTangentMode":1,"rightTangentMode":1,"broken":false}],"preWrapMode":8,"postWrapMode":8,"version":1}'
    m_Space: 2147483647
  m_Property:
    name: Scale_y
    m_serializedType:
      m_SerializableType: UnityEngine.AnimationCurve, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614628
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c117b74c5c58db542bffe25c78fe92db, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614628}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614625}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.AnimationCurve, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"frames":[{"time":0.0,"value":1.0,"inTangent":0.0,"outTangent":0.0,"tangentMode":0,"leftTangentMode":1,"rightTangentMode":1,"broken":false},{"time":1.0,"value":1.0,"inTangent":0.0,"outTangent":0.0,"tangentMode":0,"leftTangentMode":1,"rightTangentMode":1,"broken":false}],"preWrapMode":8,"postWrapMode":8,"version":1}'
    m_Space: 2147483647
  m_Property:
    name: Scale_z
    m_serializedType:
      m_SerializableType: UnityEngine.AnimationCurve, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614629
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c82227d5759e296488798b1554a72a15, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661614630}
  - {fileID: 8926484042661614631}
  - {fileID: 8926484042661614632}
  - {fileID: 8926484042661614633}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614629}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614599}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Color, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"r":8.036198616027832,"g":1.7818821668624879,"b":0.37866881489753725,"a":1.0}'
    m_Space: 2147483647
  m_Property:
    name: _Color
    m_serializedType:
      m_SerializableType: UnityEngine.Color, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614630
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614629}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614629}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: r
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614631
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614629}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614629}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: g
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614632
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614629}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614629}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: b
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614633
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614629}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614629}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: a
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614634
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 70a331b1d86cc8d4aa106ccbe0da5852, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614634}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614599}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Texture2D, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"obj":{"fileID":2800000,"guid":"b687cf73baef2a444a78397286a9a05c","type":3}}'
    m_Space: 2147483647
  m_Property:
    name: _Mask
    m_serializedType:
      m_SerializableType: UnityEngine.Texture2D, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614635
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 70a331b1d86cc8d4aa106ccbe0da5852, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614635}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614599}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Texture2D, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"obj":{"fileID":2800000,"guid":"22f4c2f8b7836954da933a2b692761a2","type":3}}'
    m_Space: 2147483647
  m_Property:
    name: _MainTex
    m_serializedType:
      m_SerializableType: UnityEngine.Texture2D, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614636
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1b2b751071c7fc14f9fa503163991826, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661614637}
  - {fileID: 8926484042661614638}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614636}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614599}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"x":-2.0,"y":0.0}'
    m_Space: 2147483647
  m_Property:
    name: _MainTexSpeed
    m_serializedType:
      m_SerializableType: UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614637
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614636}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614636}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614638
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614636}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614636}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614640
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614640}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614599}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 0
    m_Space: 2147483647
  m_Property:
    name: _AlphaClip
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614709
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1b2b751071c7fc14f9fa503163991826, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661614710}
  - {fileID: 8926484042661614711}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614709}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614599}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"x":-3.0,"y":0.0}'
    m_Space: 2147483647
  m_Property:
    name: _DistortionSpeed
    m_serializedType:
      m_SerializableType: UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614710
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614709}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614709}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614711
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614709}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614709}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614712
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614712}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614599}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 0.065
    m_Space: 2147483647
  m_Property:
    name: _DistortionAmount
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614713
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614713}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614599}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 30
    m_Space: 2147483647
  m_Property:
    name: _DistortionScale
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614714
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 73a13919d81fb7444849bae8b5c812a2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 114350483966674976}
  m_Children:
  - {fileID: 8926484042661614716}
  m_UIPosition: {x: 2057, y: -183}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots: []
  m_OutputSlots: []
  m_Label: Spawn system
  m_Data: {fileID: 8926484042661614715}
  m_InputFlowSlot:
  - link: []
  - link: []
  m_OutputFlowSlot:
  - link:
    - context: {fileID: 8926484042661614719}
      slotIndex: 0
  loopDuration: 0
  loopCount: 0
  delayBeforeLoop: 0
  delayAfterLoop: 0
--- !u!114 &8926484042661614715
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f68759077adc0b143b6e1c101e82065e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  title: 
  m_Owners:
  - {fileID: 8926484042661614714}
--- !u!114 &8926484042661614716
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5e382412bb691334bb79457a6c127924, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614714}
  m_Children: []
  m_UIPosition: {x: 604.6626, y: 222.9068}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614717}
  - {fileID: 8926484042661614718}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615062}
  repeat: 0
  spawnMode: 0
  delayMode: 0
--- !u!114 &8926484042661614717
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614717}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614716}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 1
    m_Space: 2147483647
  m_Property:
    name: Count
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614718
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614718}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614716}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 0
    m_Space: 2147483647
  m_Property:
    name: Delay
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614719
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9dfea48843f53fc438eabc12a3a30abc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 114350483966674976}
  m_Children:
  - {fileID: 8926484042661614734}
  - {fileID: 8926484042661614736}
  m_UIPosition: {x: 2057, y: 121}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614720}
  m_OutputSlots: []
  m_Label: 
  m_Data: {fileID: 8926484042661614733}
  m_InputFlowSlot:
  - link:
    - context: {fileID: 8926484042661614714}
      slotIndex: 0
  m_OutputFlowSlot:
  - link:
    - context: {fileID: 8926484042661614741}
      slotIndex: 0
--- !u!114 &8926484042661614720
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1b605c022ee79394a8a776c0869b3f9a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661614721}
  - {fileID: 8926484042661614725}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614720}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614719}
    m_Value:
      m_Type:
        m_SerializableType: UnityEditor.VFX.AABox, Unity.VisualEffectGraph.Editor,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"center":{"x":0.0,"y":1.0,"z":0.0},"size":{"x":2.0,"y":3.0,"z":2.0}}'
    m_Space: 0
  m_Property:
    name: bounds
    m_serializedType:
      m_SerializableType: UnityEditor.VFX.AABox, Unity.VisualEffectGraph.Editor,
        Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614721
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac39bd03fca81b849929b9c966f1836a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614720}
  m_Children:
  - {fileID: 8926484042661614722}
  - {fileID: 8926484042661614723}
  - {fileID: 8926484042661614724}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614720}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: center
    m_serializedType:
      m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614722
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614721}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614720}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614723
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614721}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614720}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614724
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614721}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614720}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: z
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614725
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac39bd03fca81b849929b9c966f1836a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614720}
  m_Children:
  - {fileID: 8926484042661614726}
  - {fileID: 8926484042661614727}
  - {fileID: 8926484042661614728}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614720}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: size
    m_serializedType:
      m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614726
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614725}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614720}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614727
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614725}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614720}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614728
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614725}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614720}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: z
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614733
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d78581a96eae8bf4398c282eb0b098bd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  title: 
  m_Owners:
  - {fileID: 8926484042661614719}
  - {fileID: 8926484042661614741}
  - {fileID: 8926484042661614743}
  - {fileID: 8926484042661614599}
  - {fileID: 8926484042661614877}
  dataType: 0
  capacity: 32
  stripCapacity: 16
  particlePerStripCount: 16
  needsComputeBounds: 0
  boundsMode: 1
  m_Space: 0
--- !u!114 &8926484042661614734
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a971fa2e110a0ac42ac1d8dae408704b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614719}
  m_Children: []
  m_UIPosition: {x: 838.0453, y: 280.47894}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614735}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615063}
  attribute: lifetime
  Composition: 0
  Source: 0
  Random: 0
  channels: 6
--- !u!114 &8926484042661614735
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614735}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614734}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 3
    m_Space: 2147483647
  m_Property:
    name: Lifetime
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots:
  - {fileID: 8926484042661614613}
--- !u!114 &8926484042661614736
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a971fa2e110a0ac42ac1d8dae408704b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614719}
  m_Children: []
  m_UIPosition: {x: 604.6626, y: 222.9068}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614737}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615064}
  attribute: angle
  Composition: 0
  Source: 0
  Random: 0
  channels: 6
--- !u!114 &8926484042661614737
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac39bd03fca81b849929b9c966f1836a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661614738}
  - {fileID: 8926484042661614739}
  - {fileID: 8926484042661614740}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614737}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614736}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"x":0.0,"y":0.0,"z":0.0}'
    m_Space: 2147483647
  m_Property:
    name: Angle
    m_serializedType:
      m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614738
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614737}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614737}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614739
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614737}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614737}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614740
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614737}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614737}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: z
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614741
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2dc095764ededfa4bb32fa602511ea4b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 114350483966674976}
  m_Children: []
  m_UIPosition: {x: 2058, y: 530}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots: []
  m_OutputSlots: []
  m_Label: 
  m_Data: {fileID: 8926484042661614733}
  m_InputFlowSlot:
  - link:
    - context: {fileID: 8926484042661614719}
      slotIndex: 0
  m_OutputFlowSlot:
  - link:
    - context: {fileID: 8926484042661614743}
      slotIndex: 0
    - context: {fileID: 8926484042661614599}
      slotIndex: 0
    - context: {fileID: 8926484042661614877}
      slotIndex: 0
  integration: 0
  angularIntegration: 0
  ageParticles: 1
  reapParticles: 1
  skipZeroDeltaUpdate: 0
--- !u!114 &8926484042661614743
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e837ba02e1cb47d4394b6c186d164156, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 114350483966674976}
  m_Children:
  - {fileID: 8926484042661614763}
  - {fileID: 8926484042661614769}
  - {fileID: 8926484042661614771}
  - {fileID: 8926484042661614776}
  m_UIPosition: {x: 2057, y: 697}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614744}
  - {fileID: 8926484042661614749}
  - {fileID: 8926484042661614750}
  - {fileID: 8926484042661614751}
  - {fileID: 8926484042661614754}
  - {fileID: 8926484042661614755}
  - {fileID: 8926484042661614758}
  - {fileID: 8926484042661614759}
  - {fileID: 8926484042661614760}
  - {fileID: 8926484042661614761}
  m_OutputSlots: []
  m_Label: ELECTRIC BEAM
  m_Data: {fileID: 8926484042661614733}
  m_InputFlowSlot:
  - link:
    - context: {fileID: 8926484042661614741}
      slotIndex: 0
  m_OutputFlowSlot:
  - link: []
  blendMode: 0
  cullMode: 3
  zWriteMode: 0
  zTestMode: 0
  useAlphaClipping: 0
  generateMotionVector: 0
  excludeFromTUAndAA: 0
  sortingPriority: 0
  m_SubOutputs: []
  colorMapping: 0
  uvMode: 0
  flipbookLayout: 0
  useSoftParticle: 0
  vfxSystemSortPriority: 0
  sort: 0
  sortMode: 0
  revertSorting: 0
  indirectDraw: 0
  computeCulling: 0
  frustumCulling: 0
  castShadows: 0
  useExposureWeight: 0
  needsOwnSort: 0
  shaderGraph: {fileID: -5475051401550479605, guid: 78f6abf4cc407f44a9c97b1e8b02d0ed,
    type: 3}
  materialSettings:
    m_PropertyNames: []
    m_PropertyValues: []
  MeshCount: 1
  lod: 0
--- !u!114 &8926484042661614744
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c82227d5759e296488798b1554a72a15, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661614745}
  - {fileID: 8926484042661614746}
  - {fileID: 8926484042661614747}
  - {fileID: 8926484042661614748}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614744}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614743}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Color, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"r":3.7659175395965578,"g":5.648876667022705,"b":34.296749114990237,"a":1.0}'
    m_Space: 2147483647
  m_Property:
    name: _Color
    m_serializedType:
      m_SerializableType: UnityEngine.Color, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614745
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614744}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614744}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: r
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614746
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614744}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614744}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: g
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614747
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614744}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614744}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: b
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614748
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614744}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614744}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: a
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614749
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 70a331b1d86cc8d4aa106ccbe0da5852, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614749}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614743}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Texture2D, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"obj":{"fileID":2800000,"guid":"b687cf73baef2a444a78397286a9a05c","type":3}}'
    m_Space: 2147483647
  m_Property:
    name: _Mask
    m_serializedType:
      m_SerializableType: UnityEngine.Texture2D, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614750
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 70a331b1d86cc8d4aa106ccbe0da5852, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614750}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614743}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Texture2D, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"obj":{"fileID":2800000,"guid":"22f4c2f8b7836954da933a2b692761a2","type":3}}'
    m_Space: 2147483647
  m_Property:
    name: _MainTex
    m_serializedType:
      m_SerializableType: UnityEngine.Texture2D, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614751
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1b2b751071c7fc14f9fa503163991826, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661614752}
  - {fileID: 8926484042661614753}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614751}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614743}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"x":-1.5,"y":0.0}'
    m_Space: 2147483647
  m_Property:
    name: _MainTexSpeed
    m_serializedType:
      m_SerializableType: UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614752
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614751}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614751}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614753
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614751}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614751}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614754
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614754}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614743}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 0.831
    m_Space: 2147483647
  m_Property:
    name: _AlphaClip
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614755
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1b2b751071c7fc14f9fa503163991826, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661614756}
  - {fileID: 8926484042661614757}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614755}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614743}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"x":-3.0,"y":0.0}'
    m_Space: 2147483647
  m_Property:
    name: _DistortionSpeed
    m_serializedType:
      m_SerializableType: UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614756
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614755}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614755}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614757
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614755}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614755}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614758
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614758}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614743}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 0.26
    m_Space: 2147483647
  m_Property:
    name: _DistortionAmount
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614759
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614759}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614743}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 30
    m_Space: 2147483647
  m_Property:
    name: _DistortionScale
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614760
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b47b8679b468b7347a00cdd50589bc9f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614760}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614743}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Mesh, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"obj":{"fileID":-591271329399093883,"guid":"9c4c330e4318eca40ab5c1f96494646b","type":3}}'
    m_Space: 2147483647
  m_Property:
    name: mesh
    m_serializedType:
      m_SerializableType: UnityEngine.Mesh, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614761
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c52d920e7fff73b498050a6b3c4404ca, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614761}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614743}
    m_Value:
      m_Type:
        m_SerializableType: System.UInt32, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 4294967295
    m_Space: 2147483647
  m_Property:
    name: subMeshMask
    m_serializedType:
      m_SerializableType: System.UInt32, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614763
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d16c6aeaef944094b9a1633041804207, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614743}
  m_Children: []
  m_UIPosition: {x: 604.6626, y: 222.9068}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614764}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615065}
  mode: 5
  axes: 4
--- !u!114 &8926484042661614764
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e8f2b4a846fd4c14a893cde576ad172b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661614765}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614764}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614763}
    m_Value:
      m_Type:
        m_SerializableType: UnityEditor.VFX.DirectionType, Unity.VisualEffectGraph.Editor,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"direction":{"x":0.0,"y":0.0,"z":1.0}}'
    m_Space: 0
  m_Property:
    name: Up
    m_serializedType:
      m_SerializableType: UnityEditor.VFX.DirectionType, Unity.VisualEffectGraph.Editor,
        Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614765
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac39bd03fca81b849929b9c966f1836a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614764}
  m_Children:
  - {fileID: 8926484042661614766}
  - {fileID: 8926484042661614767}
  - {fileID: 8926484042661614768}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614764}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: direction
    m_serializedType:
      m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614766
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614765}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614764}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614767
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614765}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614764}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614768
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614765}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614764}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: z
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614769
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a971fa2e110a0ac42ac1d8dae408704b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614743}
  m_Children: []
  m_UIPosition: {x: 604.6626, y: 222.9068}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614770}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615066}
  attribute: size
  Composition: 0
  Source: 0
  Random: 0
  channels: 6
--- !u!114 &8926484042661614770
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614770}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614769}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 3
    m_Space: 2147483647
  m_Property:
    name: Size
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614771
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a971fa2e110a0ac42ac1d8dae408704b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614743}
  m_Children: []
  m_UIPosition: {x: 604.6626, y: 222.9068}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614772}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615067}
  attribute: scale
  Composition: 0
  Source: 0
  Random: 0
  channels: 6
--- !u!114 &8926484042661614772
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac39bd03fca81b849929b9c966f1836a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661614773}
  - {fileID: 8926484042661614774}
  - {fileID: 8926484042661614775}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614772}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614771}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"x":0.4000000059604645,"y":2.0,"z":1.0}'
    m_Space: 2147483647
  m_Property:
    name: Scale
    m_serializedType:
      m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614773
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614772}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614772}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614774
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614772}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614772}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614775
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614772}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614772}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: z
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614776
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 01ec2c1930009b04ea08905b47262415, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614743}
  m_Children: []
  m_UIPosition: {x: 604.6626, y: 222.9068}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614777}
  - {fileID: 8926484042661614778}
  - {fileID: 8926484042661614779}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615068}
  attribute: scale
  Composition: 2
  AlphaComposition: 0
  SampleMode: 0
  Mode: 1
  ColorMode: 3
  channels: 6
--- !u!114 &8926484042661614777
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c117b74c5c58db542bffe25c78fe92db, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614777}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614776}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.AnimationCurve, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"frames":[{"time":-0.012134552001953125,"value":0.0,"inTangent":6.539741516113281,"outTangent":6.539741516113281,"tangentMode":0,"leftTangentMode":1,"rightTangentMode":1,"broken":false},{"time":0.14077669382095338,"value":1.0,"inTangent":0.570044755935669,"outTangent":0.570044755935669,"tangentMode":0,"leftTangentMode":0,"rightTangentMode":0,"broken":false},{"time":0.8952546715736389,"value":1.0125837326049805,"inTangent":-0.20825791358947755,"outTangent":-0.20825791358947755,"tangentMode":0,"leftTangentMode":0,"rightTangentMode":0,"broken":false},{"time":0.995147705078125,"value":-0.005649566650390625,"inTangent":-10.193236351013184,"outTangent":-10.193236351013184,"tangentMode":0,"leftTangentMode":1,"rightTangentMode":1,"broken":false}],"preWrapMode":8,"postWrapMode":8,"version":1}'
    m_Space: 2147483647
  m_Property:
    name: Scale_x
    m_serializedType:
      m_SerializableType: UnityEngine.AnimationCurve, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614778
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c117b74c5c58db542bffe25c78fe92db, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614778}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614776}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.AnimationCurve, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"frames":[{"time":0.00191497802734375,"value":0.12585067749023438,"inTangent":7.628503799438477,"outTangent":7.628503799438477,"tangentMode":0,"leftTangentMode":1,"rightTangentMode":1,"broken":false},{"time":0.11650485545396805,"value":1.0,"inTangent":-0.10580432415008545,"outTangent":-0.10580432415008545,"tangentMode":0,"leftTangentMode":0,"rightTangentMode":0,"broken":false},{"time":1.0,"value":1.0,"inTangent":0.0,"outTangent":0.0,"tangentMode":0,"leftTangentMode":1,"rightTangentMode":1,"broken":false}],"preWrapMode":8,"postWrapMode":8,"version":1}'
    m_Space: 2147483647
  m_Property:
    name: Scale_y
    m_serializedType:
      m_SerializableType: UnityEngine.AnimationCurve, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614779
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c117b74c5c58db542bffe25c78fe92db, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614779}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614776}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.AnimationCurve, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"frames":[{"time":0.0,"value":1.0,"inTangent":0.0,"outTangent":0.0,"tangentMode":0,"leftTangentMode":1,"rightTangentMode":1,"broken":false},{"time":1.0,"value":1.0,"inTangent":0.0,"outTangent":0.0,"tangentMode":0,"leftTangentMode":1,"rightTangentMode":1,"broken":false}],"preWrapMode":8,"postWrapMode":8,"version":1}'
    m_Space: 2147483647
  m_Property:
    name: Scale_z
    m_serializedType:
      m_SerializableType: UnityEngine.AnimationCurve, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614877
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e837ba02e1cb47d4394b6c186d164156, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 114350483966674976}
  m_Children:
  - {fileID: 8926484042661614897}
  - {fileID: 8926484042661614903}
  - {fileID: 8926484042661614905}
  - {fileID: 8926484042661614910}
  m_UIPosition: {x: 2573, y: 697}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614878}
  - {fileID: 8926484042661614883}
  - {fileID: 8926484042661614884}
  - {fileID: 8926484042661614885}
  - {fileID: 8926484042661614888}
  - {fileID: 8926484042661614889}
  - {fileID: 8926484042661614892}
  - {fileID: 8926484042661614893}
  - {fileID: 8926484042661614894}
  - {fileID: 8926484042661614895}
  m_OutputSlots: []
  m_Label: BEAM DARK
  m_Data: {fileID: 8926484042661614733}
  m_InputFlowSlot:
  - link:
    - context: {fileID: 8926484042661614741}
      slotIndex: 0
  m_OutputFlowSlot:
  - link: []
  blendMode: 1
  cullMode: 3
  zWriteMode: 0
  zTestMode: 0
  useAlphaClipping: 0
  generateMotionVector: 0
  excludeFromTUAndAA: 0
  sortingPriority: 0
  m_SubOutputs: []
  colorMapping: 0
  uvMode: 0
  flipbookLayout: 0
  useSoftParticle: 0
  vfxSystemSortPriority: 0
  sort: 0
  sortMode: 0
  revertSorting: 0
  indirectDraw: 0
  computeCulling: 0
  frustumCulling: 0
  castShadows: 0
  useExposureWeight: 0
  needsOwnSort: 0
  shaderGraph: {fileID: -5475051401550479605, guid: 78f6abf4cc407f44a9c97b1e8b02d0ed,
    type: 3}
  materialSettings:
    m_PropertyNames: []
    m_PropertyValues: []
  MeshCount: 1
  lod: 0
--- !u!114 &8926484042661614878
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c82227d5759e296488798b1554a72a15, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661614879}
  - {fileID: 8926484042661614880}
  - {fileID: 8926484042661614881}
  - {fileID: 8926484042661614882}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614878}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614877}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Color, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"r":0.10000000149011612,"g":0.012727273628115654,"b":0.005454546306282282,"a":1.0}'
    m_Space: 2147483647
  m_Property:
    name: _Color
    m_serializedType:
      m_SerializableType: UnityEngine.Color, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614879
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614878}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614878}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: r
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614880
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614878}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614878}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: g
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614881
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614878}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614878}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: b
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614882
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614878}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614878}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: a
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614883
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 70a331b1d86cc8d4aa106ccbe0da5852, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614883}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614877}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Texture2D, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"obj":{"fileID":2800000,"guid":"92f43abc69b2488468f0ffa1215a05cf","type":3}}'
    m_Space: 2147483647
  m_Property:
    name: _Mask
    m_serializedType:
      m_SerializableType: UnityEngine.Texture2D, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614884
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 70a331b1d86cc8d4aa106ccbe0da5852, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614884}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614877}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Texture2D, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"obj":{"fileID":2800000,"guid":"22f4c2f8b7836954da933a2b692761a2","type":3}}'
    m_Space: 2147483647
  m_Property:
    name: _MainTex
    m_serializedType:
      m_SerializableType: UnityEngine.Texture2D, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614885
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1b2b751071c7fc14f9fa503163991826, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661614886}
  - {fileID: 8926484042661614887}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614885}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614877}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"x":-2.0,"y":0.0}'
    m_Space: 2147483647
  m_Property:
    name: _MainTexSpeed
    m_serializedType:
      m_SerializableType: UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614886
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614885}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614885}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614887
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614885}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614885}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614888
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614888}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614877}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 0
    m_Space: 2147483647
  m_Property:
    name: _AlphaClip
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614889
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1b2b751071c7fc14f9fa503163991826, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661614890}
  - {fileID: 8926484042661614891}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614889}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614877}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"x":-3.0,"y":0.0}'
    m_Space: 2147483647
  m_Property:
    name: _DistortionSpeed
    m_serializedType:
      m_SerializableType: UnityEngine.Vector2, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614890
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614889}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614889}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614891
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614889}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614889}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614892
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614892}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614877}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 0.065
    m_Space: 2147483647
  m_Property:
    name: _DistortionAmount
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614893
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614893}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614877}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 30
    m_Space: 2147483647
  m_Property:
    name: _DistortionScale
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614894
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b47b8679b468b7347a00cdd50589bc9f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614894}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614877}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Mesh, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"obj":{"fileID":-591271329399093883,"guid":"9c4c330e4318eca40ab5c1f96494646b","type":3}}'
    m_Space: 2147483647
  m_Property:
    name: mesh
    m_serializedType:
      m_SerializableType: UnityEngine.Mesh, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614895
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c52d920e7fff73b498050a6b3c4404ca, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614895}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614877}
    m_Value:
      m_Type:
        m_SerializableType: System.UInt32, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 4294967295
    m_Space: 2147483647
  m_Property:
    name: subMeshMask
    m_serializedType:
      m_SerializableType: System.UInt32, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614897
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d16c6aeaef944094b9a1633041804207, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614877}
  m_Children: []
  m_UIPosition: {x: 2154.677, y: 26.49768}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614898}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615069}
  mode: 5
  axes: 4
--- !u!114 &8926484042661614898
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e8f2b4a846fd4c14a893cde576ad172b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661614899}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614898}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614897}
    m_Value:
      m_Type:
        m_SerializableType: UnityEditor.VFX.DirectionType, Unity.VisualEffectGraph.Editor,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"direction":{"x":0.0,"y":0.0,"z":1.0}}'
    m_Space: 0
  m_Property:
    name: Up
    m_serializedType:
      m_SerializableType: UnityEditor.VFX.DirectionType, Unity.VisualEffectGraph.Editor,
        Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614899
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac39bd03fca81b849929b9c966f1836a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614898}
  m_Children:
  - {fileID: 8926484042661614900}
  - {fileID: 8926484042661614901}
  - {fileID: 8926484042661614902}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614898}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: direction
    m_serializedType:
      m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614900
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614899}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614898}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614901
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614899}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614898}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614902
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614899}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614898}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: z
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614903
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a971fa2e110a0ac42ac1d8dae408704b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614877}
  m_Children: []
  m_UIPosition: {x: 2154.677, y: 26.49768}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614904}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615070}
  attribute: size
  Composition: 0
  Source: 0
  Random: 0
  channels: 6
--- !u!114 &8926484042661614904
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614904}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614903}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 3
    m_Space: 2147483647
  m_Property:
    name: Size
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614905
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a971fa2e110a0ac42ac1d8dae408704b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614877}
  m_Children: []
  m_UIPosition: {x: 2154.677, y: 26.49768}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614906}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615071}
  attribute: scale
  Composition: 0
  Source: 0
  Random: 0
  channels: 6
--- !u!114 &8926484042661614906
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac39bd03fca81b849929b9c966f1836a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661614907}
  - {fileID: 8926484042661614908}
  - {fileID: 8926484042661614909}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614906}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614905}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"x":0.550000011920929,"y":2.0,"z":1.0}'
    m_Space: 2147483647
  m_Property:
    name: Scale
    m_serializedType:
      m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614907
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614906}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614906}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614908
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614906}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614906}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614909
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614906}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614906}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: z
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614910
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 01ec2c1930009b04ea08905b47262415, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614877}
  m_Children: []
  m_UIPosition: {x: 2154.677, y: 26.49768}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614911}
  - {fileID: 8926484042661614912}
  - {fileID: 8926484042661614913}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615072}
  attribute: scale
  Composition: 2
  AlphaComposition: 0
  SampleMode: 0
  Mode: 1
  ColorMode: 3
  channels: 6
--- !u!114 &8926484042661614911
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c117b74c5c58db542bffe25c78fe92db, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614911}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614910}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.AnimationCurve, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"frames":[{"time":-0.012134552001953125,"value":0.0,"inTangent":6.539741516113281,"outTangent":6.539741516113281,"tangentMode":0,"leftTangentMode":1,"rightTangentMode":1,"broken":false},{"time":0.14077669382095338,"value":1.0,"inTangent":0.570044755935669,"outTangent":0.570044755935669,"tangentMode":0,"leftTangentMode":0,"rightTangentMode":0,"broken":false},{"time":0.8952546715736389,"value":1.0125837326049805,"inTangent":-0.20825791358947755,"outTangent":-0.20825791358947755,"tangentMode":0,"leftTangentMode":0,"rightTangentMode":0,"broken":false},{"time":0.995147705078125,"value":-0.005649566650390625,"inTangent":-10.193236351013184,"outTangent":-10.193236351013184,"tangentMode":0,"leftTangentMode":1,"rightTangentMode":1,"broken":false}],"preWrapMode":8,"postWrapMode":8,"version":1}'
    m_Space: 2147483647
  m_Property:
    name: Scale_x
    m_serializedType:
      m_SerializableType: UnityEngine.AnimationCurve, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614912
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c117b74c5c58db542bffe25c78fe92db, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614912}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614910}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.AnimationCurve, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"frames":[{"time":0.00191497802734375,"value":0.12585067749023438,"inTangent":7.628503799438477,"outTangent":7.628503799438477,"tangentMode":0,"leftTangentMode":1,"rightTangentMode":1,"broken":false},{"time":0.11650485545396805,"value":1.0,"inTangent":-0.10580432415008545,"outTangent":-0.10580432415008545,"tangentMode":0,"leftTangentMode":0,"rightTangentMode":0,"broken":false},{"time":1.0,"value":1.0,"inTangent":0.0,"outTangent":0.0,"tangentMode":0,"leftTangentMode":1,"rightTangentMode":1,"broken":false}],"preWrapMode":8,"postWrapMode":8,"version":1}'
    m_Space: 2147483647
  m_Property:
    name: Scale_y
    m_serializedType:
      m_SerializableType: UnityEngine.AnimationCurve, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614913
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c117b74c5c58db542bffe25c78fe92db, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614913}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614910}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.AnimationCurve, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"frames":[{"time":0.0,"value":1.0,"inTangent":0.0,"outTangent":0.0,"tangentMode":0,"leftTangentMode":1,"rightTangentMode":1,"broken":false},{"time":1.0,"value":1.0,"inTangent":0.0,"outTangent":0.0,"tangentMode":0,"leftTangentMode":1,"rightTangentMode":1,"broken":false}],"preWrapMode":8,"postWrapMode":8,"version":1}'
    m_Space: 2147483647
  m_Property:
    name: Scale_z
    m_serializedType:
      m_SerializableType: UnityEngine.AnimationCurve, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614918
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 73a13919d81fb7444849bae8b5c812a2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 114350483966674976}
  m_Children:
  - {fileID: 8926484042661614920}
  m_UIPosition: {x: 3445, y: -111}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614961}
  - {fileID: 8926484042661614962}
  m_OutputSlots: []
  m_Label: Spawn system
  m_Data: {fileID: 8926484042661614919}
  m_InputFlowSlot:
  - link: []
  - link: []
  m_OutputFlowSlot:
  - link:
    - context: {fileID: 8926484042661614922}
      slotIndex: 0
  loopDuration: 1
  loopCount: 1
  delayBeforeLoop: 0
  delayAfterLoop: 0
--- !u!114 &8926484042661614919
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f68759077adc0b143b6e1c101e82065e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  title: 
  m_Owners:
  - {fileID: 8926484042661614918}
--- !u!114 &8926484042661614920
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f05c6884b705ce14d82ae720f0ec209f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614918}
  m_Children: []
  m_UIPosition: {x: 2651.4165, y: -183.8833}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614921}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615073}
--- !u!114 &8926484042661614921
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614921}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614920}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 40
    m_Space: 2147483647
  m_Property:
    name: Rate
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614922
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9dfea48843f53fc438eabc12a3a30abc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 114350483966674976}
  m_Children:
  - {fileID: 8926484042661614937}
  - {fileID: 8926484042661614948}
  m_UIPosition: {x: 3446, y: 192}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614923}
  m_OutputSlots: []
  m_Label: 
  m_Data: {fileID: 8926484042661614936}
  m_InputFlowSlot:
  - link:
    - context: {fileID: 8926484042661614918}
      slotIndex: 0
  m_OutputFlowSlot:
  - link:
    - context: {fileID: 8926484042661614951}
      slotIndex: 0
--- !u!114 &8926484042661614923
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1b605c022ee79394a8a776c0869b3f9a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661614924}
  - {fileID: 8926484042661614928}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614923}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614922}
    m_Value:
      m_Type:
        m_SerializableType: UnityEditor.VFX.AABox, Unity.VisualEffectGraph.Editor,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"center":{"x":0.0,"y":1.0,"z":0.0},"size":{"x":2.0,"y":3.0,"z":2.0}}'
    m_Space: 0
  m_Property:
    name: bounds
    m_serializedType:
      m_SerializableType: UnityEditor.VFX.AABox, Unity.VisualEffectGraph.Editor,
        Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614924
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac39bd03fca81b849929b9c966f1836a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614923}
  m_Children:
  - {fileID: 8926484042661614925}
  - {fileID: 8926484042661614926}
  - {fileID: 8926484042661614927}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614923}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: center
    m_serializedType:
      m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614925
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614924}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614923}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614926
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614924}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614923}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614927
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614924}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614923}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: z
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614928
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac39bd03fca81b849929b9c966f1836a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614923}
  m_Children:
  - {fileID: 8926484042661614929}
  - {fileID: 8926484042661614930}
  - {fileID: 8926484042661614931}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614923}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: size
    m_serializedType:
      m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614929
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614928}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614923}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614930
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614928}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614923}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614931
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614928}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614923}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: z
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614936
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d78581a96eae8bf4398c282eb0b098bd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  title: 
  m_Owners:
  - {fileID: 8926484042661614922}
  - {fileID: 8926484042661614951}
  - {fileID: 8926484042661614953}
  dataType: 0
  capacity: 32
  stripCapacity: 16
  particlePerStripCount: 16
  needsComputeBounds: 0
  boundsMode: 1
  m_Space: 0
--- !u!114 &8926484042661614937
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a971fa2e110a0ac42ac1d8dae408704b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614922}
  m_Children: []
  m_UIPosition: {x: 2651.4165, y: -183.8833}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614938}
  - {fileID: 8926484042661614943}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615074}
  attribute: velocity
  Composition: 0
  Source: 0
  Random: 1
  channels: 6
--- !u!114 &8926484042661614938
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a9f9544b71b7dab44a4644b6807e8bf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661614939}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614938}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614937}
    m_Value:
      m_Type:
        m_SerializableType: UnityEditor.VFX.Vector, Unity.VisualEffectGraph.Editor,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"vector":{"x":-0.5,"y":-0.5,"z":20.0}}'
    m_Space: 0
  m_Property:
    name: A
    m_serializedType:
      m_SerializableType: UnityEditor.VFX.Vector, Unity.VisualEffectGraph.Editor,
        Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614939
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac39bd03fca81b849929b9c966f1836a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614938}
  m_Children:
  - {fileID: 8926484042661614940}
  - {fileID: 8926484042661614941}
  - {fileID: 8926484042661614942}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614938}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: vector
    m_serializedType:
      m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614940
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614939}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614938}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614941
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614939}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614938}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614942
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614939}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614938}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: z
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614943
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a9f9544b71b7dab44a4644b6807e8bf6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661614944}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614943}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614937}
    m_Value:
      m_Type:
        m_SerializableType: UnityEditor.VFX.Vector, Unity.VisualEffectGraph.Editor,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"vector":{"x":0.5,"y":0.5,"z":40.0}}'
    m_Space: 0
  m_Property:
    name: B
    m_serializedType:
      m_SerializableType: UnityEditor.VFX.Vector, Unity.VisualEffectGraph.Editor,
        Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614944
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac39bd03fca81b849929b9c966f1836a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614943}
  m_Children:
  - {fileID: 8926484042661614945}
  - {fileID: 8926484042661614946}
  - {fileID: 8926484042661614947}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614943}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: vector
    m_serializedType:
      m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614945
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614944}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614943}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614946
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614944}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614943}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614947
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614944}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614943}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: z
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614948
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a971fa2e110a0ac42ac1d8dae408704b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614922}
  m_Children: []
  m_UIPosition: {x: 2651.4165, y: -183.8833}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614949}
  - {fileID: 8926484042661614950}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615075}
  attribute: lifetime
  Composition: 0
  Source: 0
  Random: 2
  channels: 6
--- !u!114 &8926484042661614949
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614949}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614948}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 0.2
    m_Space: 2147483647
  m_Property:
    name: A
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614950
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614950}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614948}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 0.45
    m_Space: 2147483647
  m_Property:
    name: B
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614951
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2dc095764ededfa4bb32fa602511ea4b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 114350483966674976}
  m_Children: []
  m_UIPosition: {x: 3459, y: 843}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots: []
  m_OutputSlots: []
  m_Label: 
  m_Data: {fileID: 8926484042661614936}
  m_InputFlowSlot:
  - link:
    - context: {fileID: 8926484042661614922}
      slotIndex: 0
  m_OutputFlowSlot:
  - link:
    - context: {fileID: 8926484042661614953}
      slotIndex: 0
  integration: 0
  angularIntegration: 0
  ageParticles: 1
  reapParticles: 1
  skipZeroDeltaUpdate: 0
--- !u!114 &8926484042661614953
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0b9e6b9139e58d4c957ec54595da7d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 114350483966674976}
  m_Children:
  - {fileID: 8926484042661614956}
  - {fileID: 8926484042661614989}
  - {fileID: 8926484042661614993}
  - {fileID: 8926484042661614957}
  - {fileID: 8926484042661614998}
  - {fileID: 8926484042661614959}
  m_UIPosition: {x: 3459, y: 1065}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614954}
  m_OutputSlots: []
  m_Label: 
  m_Data: {fileID: 8926484042661614936}
  m_InputFlowSlot:
  - link:
    - context: {fileID: 8926484042661614951}
      slotIndex: 0
  m_OutputFlowSlot:
  - link: []
  blendMode: 0
  cullMode: 0
  zWriteMode: 0
  zTestMode: 0
  useAlphaClipping: 0
  generateMotionVector: 0
  excludeFromTUAndAA: 0
  sortingPriority: 0
  m_SubOutputs: []
  colorMapping: 0
  uvMode: 0
  flipbookLayout: 0
  useSoftParticle: 0
  vfxSystemSortPriority: 0
  sort: 0
  sortMode: 0
  revertSorting: 0
  indirectDraw: 0
  computeCulling: 0
  frustumCulling: 0
  castShadows: 0
  useExposureWeight: 0
  needsOwnSort: 0
  shaderGraph: {fileID: 0}
  materialSettings:
    m_PropertyNames: []
    m_PropertyValues: []
  primitiveType: 1
  useGeometryShader: 0
--- !u!114 &8926484042661614954
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 70a331b1d86cc8d4aa106ccbe0da5852, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614954}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614953}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Texture2D, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"obj":{"fileID":10300,"guid":"0000000000000000f000000000000000","type":0}}'
    m_Space: 2147483647
  m_Property:
    name: mainTexture
    m_serializedType:
      m_SerializableType: UnityEngine.Texture2D, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614956
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d16c6aeaef944094b9a1633041804207, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614953}
  m_Children: []
  m_UIPosition: {x: 2651.4165, y: -183.8833}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots: []
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615076}
  mode: 6
  axes: 4
--- !u!114 &8926484042661614957
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 01ec2c1930009b04ea08905b47262415, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614953}
  m_Children: []
  m_UIPosition: {x: 2651.4165, y: -183.8833}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614958}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615079}
  attribute: size
  Composition: 2
  AlphaComposition: 0
  SampleMode: 0
  Mode: 1
  ColorMode: 3
  channels: 0
--- !u!114 &8926484042661614958
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c117b74c5c58db542bffe25c78fe92db, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614958}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614957}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.AnimationCurve, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"frames":[{"time":0.0,"value":1.0,"inTangent":-0.019654136151075364,"outTangent":-0.019654136151075364,"tangentMode":0,"leftTangentMode":0,"rightTangentMode":0,"broken":false},{"time":1.0,"value":0.0,"inTangent":-2.1044981479644777,"outTangent":-2.1044981479644777,"tangentMode":0,"leftTangentMode":0,"rightTangentMode":0,"broken":false}],"preWrapMode":8,"postWrapMode":8,"version":1}'
    m_Space: 2147483647
  m_Property:
    name: Size
    m_serializedType:
      m_SerializableType: UnityEngine.AnimationCurve, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614959
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 01ec2c1930009b04ea08905b47262415, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614953}
  m_Children: []
  m_UIPosition: {x: 2651.4165, y: -183.8833}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614960}
  m_OutputSlots: []
  m_Disabled: 1
  m_ActivationSlot: {fileID: 8926484042661615081}
  attribute: color
  Composition: 0
  AlphaComposition: 0
  SampleMode: 0
  Mode: 1
  ColorMode: 3
  channels: 6
--- !u!114 &8926484042661614960
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76f778ff57c4e8145b9681fe3268d8e9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614960}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614959}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Gradient, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"colorKeys":[{"color":{"r":1.0,"g":1.0,"b":1.0,"a":1.0},"time":0.0},{"color":{"r":1.0,"g":1.0,"b":1.0,"a":1.0},"time":1.0}],"alphaKeys":[{"alpha":0.0,"time":0.0},{"alpha":1.0,"time":0.10000763088464737},{"alpha":1.0,"time":0.800000011920929},{"alpha":0.0,"time":1.0}],"gradientMode":0}'
    m_Space: 2147483647
  m_Property:
    name: Color
    m_serializedType:
      m_SerializableType: UnityEngine.Gradient, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614961
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614961}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614918}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 0.1
    m_Space: 2147483647
  m_Property:
    name: LoopDuration
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots:
  - {fileID: 8926484042661614966}
--- !u!114 &8926484042661614962
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4d246e354feb93041a837a9ef59437cb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614962}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614918}
    m_Value:
      m_Type:
        m_SerializableType: System.Int32, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 1
    m_Space: 2147483647
  m_Property:
    name: LoopCount
    m_serializedType:
      m_SerializableType: System.Int32, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614963
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0155ae97d9a75e3449c6d0603b79c2f4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 114350483966674976}
  m_Children: []
  m_UIPosition: {x: 3222, y: -67}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614964}
  - {fileID: 8926484042661614965}
  m_OutputSlots:
  - {fileID: 8926484042661614966}
  m_Operands:
  - name: a
    type:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  - name: b
    type:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
--- !u!114 &8926484042661614964
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614964}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614963}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 0
    m_Space: 2147483647
  m_Property:
    name: a
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots:
  - {fileID: 8926484042661614613}
--- !u!114 &8926484042661614965
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614965}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614963}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 0
    m_Space: 2147483647
  m_Property:
    name: b
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots:
  - {fileID: 8926484042661614970}
--- !u!114 &8926484042661614966
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614966}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614963}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: 
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 1
  m_LinkedSlots:
  - {fileID: 8926484042661614961}
--- !u!114 &8926484042661614967
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b8ee8a7543fa09e42a7c8616f60d2ad7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 114350483966674976}
  m_Children: []
  m_UIPosition: {x: 3034, y: 46}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614968}
  - {fileID: 8926484042661614969}
  m_OutputSlots:
  - {fileID: 8926484042661614970}
  m_Operands:
  - name: a
    type:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  - name: b
    type:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
--- !u!114 &8926484042661614968
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614968}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614967}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 1
    m_Space: 2147483647
  m_Property:
    name: a
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots:
  - {fileID: 8926484042661614613}
--- !u!114 &8926484042661614969
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614969}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614967}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 0.08
    m_Space: 2147483647
  m_Property:
    name: b
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614970
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614970}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614967}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: 
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 1
  m_LinkedSlots:
  - {fileID: 8926484042661614965}
--- !u!114 &8926484042661614989
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a971fa2e110a0ac42ac1d8dae408704b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614953}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614991}
  - {fileID: 8926484042661614992}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615077}
  attribute: size
  Composition: 0
  Source: 0
  Random: 2
  channels: 6
--- !u!114 &8926484042661614991
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614991}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614989}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 0.7
    m_Space: 2147483647
  m_Property:
    name: A
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614992
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614992}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614989}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 1.2
    m_Space: 2147483647
  m_Property:
    name: B
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614993
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a971fa2e110a0ac42ac1d8dae408704b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614953}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614994}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615078}
  attribute: scale
  Composition: 0
  Source: 0
  Random: 0
  channels: 6
--- !u!114 &8926484042661614994
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac39bd03fca81b849929b9c966f1836a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661614995}
  - {fileID: 8926484042661614996}
  - {fileID: 8926484042661614997}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614994}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614993}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"x":0.05000000074505806,"y":1.0,"z":1.0}'
    m_Space: 2147483647
  m_Property:
    name: Scale
    m_serializedType:
      m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614995
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614994}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614994}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614996
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614994}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614994}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614997
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614994}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614994}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: z
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661614998
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a971fa2e110a0ac42ac1d8dae408704b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614953}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661614999}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615080}
  attribute: color
  Composition: 0
  Source: 0
  Random: 0
  channels: 6
--- !u!114 &8926484042661614999
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac39bd03fca81b849929b9c966f1836a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661615000}
  - {fileID: 8926484042661615001}
  - {fileID: 8926484042661615002}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614999}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614998}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"x":50.55712890625,"y":12.027704238891602,"z":4.077188014984131}'
    m_Space: 2147483647
  m_Property:
    name: Color
    m_serializedType:
      m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614999}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614999}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615001
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614999}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614999}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615002
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661614999}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661614999}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: z
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615003
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 73a13919d81fb7444849bae8b5c812a2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 114350483966674976}
  m_Children:
  - {fileID: 8926484042661615005}
  m_UIPosition: {x: 4538, y: 259}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661615051}
  - {fileID: 8926484042661615052}
  m_OutputSlots: []
  m_Label: Spawn system
  m_Data: {fileID: 8926484042661615004}
  m_InputFlowSlot:
  - link: []
  - link: []
  m_OutputFlowSlot:
  - link:
    - context: {fileID: 8926484042661615007}
      slotIndex: 0
  loopDuration: 1
  loopCount: 1
  delayBeforeLoop: 0
  delayAfterLoop: 0
--- !u!114 &8926484042661615004
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f68759077adc0b143b6e1c101e82065e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  title: 
  m_Owners:
  - {fileID: 8926484042661615003}
--- !u!114 &8926484042661615005
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f05c6884b705ce14d82ae720f0ec209f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661615003}
  m_Children: []
  m_UIPosition: {x: 4026.835, y: -133.8617}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661615006}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615082}
--- !u!114 &8926484042661615006
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615006}
  m_MasterData:
    m_Owner: {fileID: 8926484042661615005}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 16
    m_Space: 2147483647
  m_Property:
    name: Rate
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615007
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9dfea48843f53fc438eabc12a3a30abc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 114350483966674976}
  m_Children:
  - {fileID: 8926484042661615033}
  m_UIPosition: {x: 4539, y: 563}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661615008}
  m_OutputSlots: []
  m_Label: 
  m_Data: {fileID: 8926484042661615021}
  m_InputFlowSlot:
  - link:
    - context: {fileID: 8926484042661615003}
      slotIndex: 0
  m_OutputFlowSlot:
  - link:
    - context: {fileID: 8926484042661615036}
      slotIndex: 0
--- !u!114 &8926484042661615008
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1b605c022ee79394a8a776c0869b3f9a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661615009}
  - {fileID: 8926484042661615013}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615008}
  m_MasterData:
    m_Owner: {fileID: 8926484042661615007}
    m_Value:
      m_Type:
        m_SerializableType: UnityEditor.VFX.AABox, Unity.VisualEffectGraph.Editor,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"center":{"x":0.0,"y":1.0,"z":0.0},"size":{"x":2.0,"y":3.0,"z":2.0}}'
    m_Space: 0
  m_Property:
    name: bounds
    m_serializedType:
      m_SerializableType: UnityEditor.VFX.AABox, Unity.VisualEffectGraph.Editor,
        Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615009
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac39bd03fca81b849929b9c966f1836a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661615008}
  m_Children:
  - {fileID: 8926484042661615010}
  - {fileID: 8926484042661615011}
  - {fileID: 8926484042661615012}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615008}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: center
    m_serializedType:
      m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615010
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661615009}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615008}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615011
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661615009}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615008}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615012
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661615009}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615008}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: z
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615013
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac39bd03fca81b849929b9c966f1836a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661615008}
  m_Children:
  - {fileID: 8926484042661615014}
  - {fileID: 8926484042661615015}
  - {fileID: 8926484042661615016}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615008}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: size
    m_serializedType:
      m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615014
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661615013}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615008}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615015
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661615013}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615008}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615016
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661615013}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615008}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: z
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615021
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d78581a96eae8bf4398c282eb0b098bd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  title: 
  m_Owners:
  - {fileID: 8926484042661615007}
  - {fileID: 8926484042661615036}
  - {fileID: 8926484042661615038}
  dataType: 0
  capacity: 100
  stripCapacity: 16
  particlePerStripCount: 16
  needsComputeBounds: 0
  boundsMode: 1
  m_Space: 0
--- !u!114 &8926484042661615033
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a971fa2e110a0ac42ac1d8dae408704b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661615007}
  m_Children: []
  m_UIPosition: {x: 4026.835, y: -133.8617}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661615034}
  - {fileID: 8926484042661615035}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615083}
  attribute: lifetime
  Composition: 0
  Source: 0
  Random: 2
  channels: 6
--- !u!114 &8926484042661615034
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615034}
  m_MasterData:
    m_Owner: {fileID: 8926484042661615033}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 0.1
    m_Space: 2147483647
  m_Property:
    name: A
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615035
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615035}
  m_MasterData:
    m_Owner: {fileID: 8926484042661615033}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 0.2
    m_Space: 2147483647
  m_Property:
    name: B
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615036
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2dc095764ededfa4bb32fa602511ea4b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 114350483966674976}
  m_Children: []
  m_UIPosition: {x: 4540, y: 956}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots: []
  m_OutputSlots: []
  m_Label: 
  m_Data: {fileID: 8926484042661615021}
  m_InputFlowSlot:
  - link:
    - context: {fileID: 8926484042661615007}
      slotIndex: 0
  m_OutputFlowSlot:
  - link:
    - context: {fileID: 8926484042661615038}
      slotIndex: 0
  integration: 0
  angularIntegration: 0
  ageParticles: 1
  reapParticles: 1
  skipZeroDeltaUpdate: 0
--- !u!114 &8926484042661615038
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0b9e6b9139e58d4c957ec54595da7d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 114350483966674976}
  m_Children:
  - {fileID: 8926484042661615041}
  - {fileID: 8926484042661615047}
  - {fileID: 8926484042661615042}
  - {fileID: 8926484042661615053}
  - {fileID: 8926484042661615044}
  m_UIPosition: {x: 4541, y: 1106}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661615046}
  - {fileID: 8926484042661615039}
  m_OutputSlots: []
  m_Label: 
  m_Data: {fileID: 8926484042661615021}
  m_InputFlowSlot:
  - link:
    - context: {fileID: 8926484042661615036}
      slotIndex: 0
  m_OutputFlowSlot:
  - link: []
  blendMode: 0
  cullMode: 0
  zWriteMode: 0
  zTestMode: 0
  useAlphaClipping: 0
  generateMotionVector: 0
  excludeFromTUAndAA: 0
  sortingPriority: 0
  m_SubOutputs: []
  colorMapping: 0
  uvMode: 0
  flipbookLayout: 0
  useSoftParticle: 1
  vfxSystemSortPriority: 0
  sort: 0
  sortMode: 0
  revertSorting: 0
  indirectDraw: 0
  computeCulling: 0
  frustumCulling: 0
  castShadows: 0
  useExposureWeight: 0
  needsOwnSort: 0
  shaderGraph: {fileID: 0}
  materialSettings:
    m_PropertyNames: []
    m_PropertyValues: []
  primitiveType: 1
  useGeometryShader: 0
--- !u!114 &8926484042661615039
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 70a331b1d86cc8d4aa106ccbe0da5852, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615039}
  m_MasterData:
    m_Owner: {fileID: 8926484042661615038}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Texture2D, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"obj":{"fileID":10300,"guid":"0000000000000000f000000000000000","type":0}}'
    m_Space: 2147483647
  m_Property:
    name: mainTexture
    m_serializedType:
      m_SerializableType: UnityEngine.Texture2D, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615041
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d16c6aeaef944094b9a1633041804207, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661615038}
  m_Children: []
  m_UIPosition: {x: 4026.835, y: -133.8617}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots: []
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615084}
  mode: 0
  axes: 4
--- !u!114 &8926484042661615042
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 01ec2c1930009b04ea08905b47262415, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661615038}
  m_Children: []
  m_UIPosition: {x: 4026.835, y: -133.8617}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661615043}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615086}
  attribute: size
  Composition: 2
  AlphaComposition: 0
  SampleMode: 0
  Mode: 1
  ColorMode: 3
  channels: 0
--- !u!114 &8926484042661615043
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c117b74c5c58db542bffe25c78fe92db, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615043}
  m_MasterData:
    m_Owner: {fileID: 8926484042661615042}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.AnimationCurve, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"frames":[{"time":0.0,"value":1.0,"inTangent":-0.019654136151075364,"outTangent":-0.019654136151075364,"tangentMode":0,"leftTangentMode":0,"rightTangentMode":0,"broken":false},{"time":1.0,"value":0.0,"inTangent":-2.1044981479644777,"outTangent":-2.1044981479644777,"tangentMode":0,"leftTangentMode":0,"rightTangentMode":0,"broken":false}],"preWrapMode":8,"postWrapMode":8,"version":1}'
    m_Space: 2147483647
  m_Property:
    name: Size
    m_serializedType:
      m_SerializableType: UnityEngine.AnimationCurve, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615044
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 01ec2c1930009b04ea08905b47262415, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661615038}
  m_Children: []
  m_UIPosition: {x: 4026.835, y: -133.8617}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661615045}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615088}
  attribute: color
  Composition: 2
  AlphaComposition: 2
  SampleMode: 0
  Mode: 1
  ColorMode: 3
  channels: 6
--- !u!114 &8926484042661615045
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76f778ff57c4e8145b9681fe3268d8e9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615045}
  m_MasterData:
    m_Owner: {fileID: 8926484042661615044}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Gradient, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"colorKeys":[{"color":{"r":1.0,"g":1.0,"b":1.0,"a":1.0},"time":0.0},{"color":{"r":1.0,"g":1.0,"b":1.0,"a":1.0},"time":1.0}],"alphaKeys":[{"alpha":0.0,"time":0.0},{"alpha":1.0,"time":0.10000763088464737},{"alpha":1.0,"time":0.32646676898002627},{"alpha":0.0,"time":1.0}],"gradientMode":0}'
    m_Space: 2147483647
  m_Property:
    name: Color
    m_serializedType:
      m_SerializableType: UnityEngine.Gradient, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615046
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615046}
  m_MasterData:
    m_Owner: {fileID: 8926484042661615038}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 1
    m_Space: 2147483647
  m_Property:
    name: softParticleFadeDistance
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615047
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a971fa2e110a0ac42ac1d8dae408704b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661615038}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661615049}
  - {fileID: 8926484042661615050}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615085}
  attribute: size
  Composition: 0
  Source: 0
  Random: 2
  channels: 6
--- !u!114 &8926484042661615049
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615049}
  m_MasterData:
    m_Owner: {fileID: 8926484042661615047}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 2.5
    m_Space: 2147483647
  m_Property:
    name: A
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615050
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615050}
  m_MasterData:
    m_Owner: {fileID: 8926484042661615047}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 5
    m_Space: 2147483647
  m_Property:
    name: B
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615051
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615051}
  m_MasterData:
    m_Owner: {fileID: 8926484042661615003}
    m_Value:
      m_Type:
        m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 0.1
    m_Space: 2147483647
  m_Property:
    name: LoopDuration
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots:
  - {fileID: 8926484042661614613}
--- !u!114 &8926484042661615052
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4d246e354feb93041a837a9ef59437cb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615052}
  m_MasterData:
    m_Owner: {fileID: 8926484042661615003}
    m_Value:
      m_Type:
        m_SerializableType: System.Int32, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: 1
    m_Space: 2147483647
  m_Property:
    name: LoopCount
    m_serializedType:
      m_SerializableType: System.Int32, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615053
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a971fa2e110a0ac42ac1d8dae408704b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661615038}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 0
  m_UISuperCollapsed: 0
  m_InputSlots:
  - {fileID: 8926484042661615054}
  m_OutputSlots: []
  m_Disabled: 0
  m_ActivationSlot: {fileID: 8926484042661615087}
  attribute: color
  Composition: 0
  Source: 0
  Random: 0
  channels: 6
--- !u!114 &8926484042661615054
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac39bd03fca81b849929b9c966f1836a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children:
  - {fileID: 8926484042661615055}
  - {fileID: 8926484042661615056}
  - {fileID: 8926484042661615057}
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615054}
  m_MasterData:
    m_Owner: {fileID: 8926484042661615053}
    m_Value:
      m_Type:
        m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
          Culture=neutral, PublicKeyToken=null
      m_SerializableObject: '{"x":6.422234535217285,"y":1.4794676303863526,"z":0.4707396924495697}'
    m_Space: 2147483647
  m_Property:
    name: Color
    m_serializedType:
      m_SerializableType: UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0,
        Culture=neutral, PublicKeyToken=null
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615055
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661615054}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615054}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: x
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615056
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661615054}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615054}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: y
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615057
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f780aa281814f9842a7c076d436932e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 8926484042661615054}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615054}
  m_MasterData:
    m_Owner: {fileID: 0}
    m_Value:
      m_Type:
        m_SerializableType: 
      m_SerializableObject: 
    m_Space: 2147483647
  m_Property:
    name: z
    m_serializedType:
      m_SerializableType: System.Single, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615058
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615058}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614614}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615059
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615059}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614604}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615060
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615060}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614620}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615061
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615061}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614625}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615062
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615062}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614716}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615063
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615063}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614734}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615064
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615064}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614736}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615065
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615065}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614763}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615066
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615066}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614769}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615067
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615067}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614771}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615068
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615068}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614776}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615069
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615069}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614897}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615070
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615070}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614903}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615071
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615071}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614905}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615072
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615072}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614910}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615073
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615073}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614920}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615074
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615074}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614937}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615075
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615075}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614948}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615076
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615076}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614956}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615077
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615077}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614989}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615078
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615078}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614993}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615079
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615079}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614957}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615080
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615080}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614998}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615081
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615081}
  m_MasterData:
    m_Owner: {fileID: 8926484042661614959}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: False
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615082
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615082}
  m_MasterData:
    m_Owner: {fileID: 8926484042661615005}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615083
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615083}
  m_MasterData:
    m_Owner: {fileID: 8926484042661615033}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615084
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615084}
  m_MasterData:
    m_Owner: {fileID: 8926484042661615041}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615085
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615085}
  m_MasterData:
    m_Owner: {fileID: 8926484042661615047}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615086
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615086}
  m_MasterData:
    m_Owner: {fileID: 8926484042661615042}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615087
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615087}
  m_MasterData:
    m_Owner: {fileID: 8926484042661615053}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
--- !u!114 &8926484042661615088
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b4c11ff25089a324daf359f4b0629b33, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UIIgnoredErrors: []
  m_Parent: {fileID: 0}
  m_Children: []
  m_UIPosition: {x: 0, y: 0}
  m_UICollapsed: 1
  m_UISuperCollapsed: 0
  m_MasterSlot: {fileID: 8926484042661615088}
  m_MasterData:
    m_Owner: {fileID: 8926484042661615044}
    m_Value:
      m_Type:
        m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
          PublicKeyToken=b77a5c561934e089
      m_SerializableObject: True
    m_Space: 2147483647
  m_Property:
    name: _vfx_enabled
    m_serializedType:
      m_SerializableType: System.Boolean, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
  m_Direction: 0
  m_LinkedSlots: []
