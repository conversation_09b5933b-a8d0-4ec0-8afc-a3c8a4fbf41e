%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-7939626805046066630
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 7
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Retro Skybox Procedural
  m_Shader: {fileID: -6465566751694194690, guid: 5e4dc85abd18a5a4ea90867121baf2e1, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _SURFACE_TYPE_TRANSPARENT
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaClip: 0
    - _AlphaToMask: 0
    - _Blend: 0
    - _CastShadows: 0
    - _Cloud_Size: 25
    - _Cloud_Size_2: 5
    - _Color_Mix_Power: 1
    - _Cull: 1
    - _DstBlend: 10
    - _QueueControl: 0
    - _QueueOffset: 0
    - _Resolution: 32
    - _Resolution_Limit: 64
    - _Snaps_Per_Unit: 48
    - _SrcBlend: 5
    - _Star_Density: 5
    - _Star_Power: 100
    - _Surface: 1
    - _ZTest: 4
    - _ZWrite: 0
    - _ZWriteControl: 0
    m_Colors:
    - _Cloud_Color: {r: 1, g: 1, b: 1, a: 1}
    - _Cloud_Height_Thresholds: {r: 0.1, g: 0.5, b: 0, a: 0}
    - _Cloud_Sizes: {r: 5, g: 25, b: 0, a: 0}
    - _Cloud_Velocity: {r: -0.01, g: -0.025, b: 0, a: 0}
    - _Cloud_Visibility_Thresholds: {r: 0, g: 0.35, b: 0, a: 0}
    - _Ground_Color: {r: 0.049172327, g: 0.16054267, b: 0.8018868, a: 1}
    - _Sky_Color: {r: 0.17454815, g: 0, b: 1, a: 1}
    - _Star_Color: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
