%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-8398484505846228569
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c7844df956262394994083ddeb428870, type: 3}
  m_Name: UltimateVignette_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &-8193097788491467062
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4c0d5ef10a7e0d44793f6dc791b1bff1, type: 3}
  m_Name: Warp_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &-7557192908794760846
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6d613f08f173d4dd895bb07b3230baa9, type: 3}
  m_Name: FullScreenPassRendererFeature
  m_EditorClassIdentifier: 
  m_Active: 1
  injectionPoint: 600
  fetchColorBuffer: 1
  requirements: 0
  passMaterial: {fileID: 2100000, guid: 818eeb2226ace4746b787fa2da2d2ca4, type: 2}
  passIndex: 0
  bindDepthStencilAttachment: 0
  m_Version: 1
--- !u!114 &-6617621267867530613
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a1614fc811f8f184697d9bee70ab9fe5, type: 3}
  m_Name: DecalRendererFeature
  m_EditorClassIdentifier: 
  m_Active: 1
  m_Settings:
    technique: 0
    maxDrawDistance: 1000
    decalLayers: 1
    dBufferSettings:
      surfaceData: 2
    screenSpaceSettings:
      normalBlend: 2
  m_CopyDepthPS: {fileID: 4800000, guid: d6dae50ee9e1bfa4db75f19f99355220, type: 3}
  m_DBufferClear: {fileID: 4800000, guid: f056d8bd2a1c7e44e9729144b4c70395, type: 3}
--- !u!114 &-5981794544008449540
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1d7bb6734e007491eaf87110789f5de0, type: 3}
  m_Name: EdgeNoise_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &-5856880882066975755
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9caddd0a630dc4340a7a825f696ed5f9, type: 3}
  m_Name: VHSEffect_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &-3414702845194800854
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 33d07fd1fbf1649469b3f0595ed371ac, type: 3}
  m_Name: RetroCustomEffectRLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &-3312902387941048059
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 601d4ed4ece5c44128d543548a424bfd, type: 3}
  m_Name: Fisheye_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &-3239367548973483504
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 556568a008c5845c2b9aff7eeaa6e59a, type: 3}
  m_Name: Glitch2
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &-2954007144801514562
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5a5aa0b62f42f444994ed38e7cc43aa9, type: 3}
  m_Name: Jitter_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &-2406573504987694326
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2f75c725b86c641cfb2fd4f9809eacc4, type: 3}
  m_Name: Bleed_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &-1238334294965179425
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 086121500fc2a42c09847ca64876bd91, type: 3}
  m_Name: CinematicBars_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &-1225271240116836136
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d4c7a478b66c94e77b1960cd0a1d5d71, type: 3}
  m_Name: CRTAperture_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &-729484397564305934
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 790bcd5ee75b9fb4c997ed0938750856, type: 3}
  m_Name: CRT
  m_EditorClassIdentifier: 
  m_Active: 1
--- !u!114 &-642895830750686904
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4d5f493bd0ee2420eb6634b618413815, type: 3}
  m_Name: Phosphor_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &-194070395210606168
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f9dfbf9b80cf647a3a5806dba5c025fb, type: 3}
  m_Name: NTSCEncode_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: de640fe3d0db1804a85f9fc8f5cadab6, type: 3}
  m_Name: URP_Renderer
  m_EditorClassIdentifier: 
  debugShaders:
    debugReplacementPS: {fileID: 4800000, guid: cf852408f2e174538bcd9b7fda1c5ae7, type: 3}
    hdrDebugViewPS: {fileID: 4800000, guid: 573620ae32aec764abd4d728906d2587, type: 3}
  m_RendererFeatures:
  - {fileID: -6617621267867530613}
  - {fileID: 3992106644357486468}
  - {fileID: -3239367548973483504}
  - {fileID: 4580510352530173857}
  - {fileID: 4109851466401226335}
  - {fileID: 8158104216630410722}
  - {fileID: 366238664421994161}
  - {fileID: 4051207675952980936}
  - {fileID: -642895830750686904}
  - {fileID: -2954007144801514562}
  - {fileID: 3689876581050981082}
  - {fileID: 6391833273432820578}
  - {fileID: -5981794544008449540}
  - {fileID: 4796484695405141665}
  - {fileID: -8398484505846228569}
  - {fileID: 177082197139813710}
  - {fileID: -3312902387941048059}
  - {fileID: 5256962531726100406}
  - {fileID: -5856880882066975755}
  - {fileID: 4439151865633544354}
  - {fileID: 6161772826145683456}
  - {fileID: 5409837567437277680}
  - {fileID: -1225271240116836136}
  - {fileID: -194070395210606168}
  - {fileID: 7710238506235992648}
  - {fileID: -8193097788491467062}
  - {fileID: 4569238106607823226}
  - {fileID: -2406573504987694326}
  - {fileID: -1238334294965179425}
  - {fileID: 3413614886154339292}
  - {fileID: -7557192908794760846}
  - {fileID: -729484397564305934}
  m_RendererFeatureMap: 8b8aafbe847c29a48497678ad7cf663710fa818a60740bd3a16f8fa4e53d913f5f9e763e22200939e23ddfc08f683771b1eee2f71f241505c86383c4eac73838489186eca5f913f7becb231e274201d7da9a935e33133533624b7c2e7f5ab458fcb1011d8e65fcaca19e38e86a899042a7d969c9d696728b4e7590f0511f750205013ba7d43406d2b68712ed917bf448f5139f6cde2db8aea29819271a099b3d00781129bd038355f089a9e39a9a134bd86855834cf6feeea8590df800864efd481abc211245006bca76ed62f8444c8e7aa9895ed931693f0a7b57d525229adedfab6a0b858dd0eedc033721a7985f2f72f12fd416751f97
  m_UseNativeRenderPass: 0
  postProcessData: {fileID: 11400000, guid: 41439944d30ece34e96484bdb6645b55, type: 2}
  xrSystemData: {fileID: 11400000, guid: 60e1133243b97e347b653163a8c01b64, type: 2}
  shaders:
    blitPS: {fileID: 4800000, guid: c17132b1f77d20942aa75f8429c0f8bc, type: 3}
    copyDepthPS: {fileID: 4800000, guid: d6dae50ee9e1bfa4db75f19f99355220, type: 3}
    screenSpaceShadowPS: {fileID: 0}
    samplingPS: {fileID: 4800000, guid: 04c410c9937594faa893a11dceb85f7e, type: 3}
    stencilDeferredPS: {fileID: 4800000, guid: e9155b26e1bc55942a41e518703fe304, type: 3}
    fallbackErrorPS: {fileID: 4800000, guid: e6e9a19c3678ded42a3bc431ebef7dbd, type: 3}
    fallbackLoadingPS: {fileID: 4800000, guid: 7f888aff2ac86494babad1c2c5daeee2, type: 3}
    materialErrorPS: {fileID: 4800000, guid: 5fd9a8feb75a4b5894c241777f519d4e, type: 3}
    coreBlitPS: {fileID: 4800000, guid: 93446b5c5339d4f00b85c159e1159b7c, type: 3}
    coreBlitColorAndDepthPS: {fileID: 4800000, guid: d104b2fc1ca6445babb8e90b0758136b, type: 3}
    blitHDROverlay: {fileID: 4800000, guid: a89bee29cffa951418fc1e2da94d1959, type: 3}
    cameraMotionVector: {fileID: 4800000, guid: c56b7e0d4c7cb484e959caeeedae9bbf, type: 3}
    objectMotionVector: {fileID: 4800000, guid: 7b3ede40266cd49a395def176e1bc486, type: 3}
    dataDrivenLensFlare: {fileID: 4800000, guid: 6cda457ac28612740adb23da5d39ea92, type: 3}
    terrainDetailLitPS: {fileID: 4800000, guid: f6783ab646d374f94b199774402a5144, type: 3}
    terrainDetailGrassPS: {fileID: 4800000, guid: e507fdfead5ca47e8b9a768b51c291a1, type: 3}
    terrainDetailGrassBillboardPS: {fileID: 4800000, guid: 29868e73b638e48ca99a19ea58c48d90, type: 3}
  m_AssetVersion: 2
  m_OpaqueLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_TransparentLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_DefaultStencilState:
    overrideStencilState: 0
    stencilReference: 0
    stencilCompareFunction: 8
    passOperation: 2
    failOperation: 0
    zFailOperation: 0
  m_ShadowTransparentReceive: 1
  m_RenderingMode: 0
  m_DepthPrimingMode: 0
  m_CopyDepthMode: 1
  m_AccurateGbufferNormals: 0
  m_IntermediateTextureMode: 1
--- !u!114 &177082197139813710
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c38fe044de5d4c940ac41c8465ae3531, type: 3}
  m_Name: PulsatingVignette_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &366238664421994161
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7d951085bd8f5804582bb7fe032ca6c2, type: 3}
  m_Name: Noise2Effect_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &3413614886154339292
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c4de6056f8db14695921a1672cfd7acc, type: 3}
  m_Name: CustomTexture_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &3689876581050981082
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 75b66763eec44e84fbffb152b15a8738, type: 3}
  m_Name: VHSScanlines_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &3992106644357486468
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a34f9733ef80345e782a33abcb47a1f7, type: 3}
  m_Name: Glitch1
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &4051207675952980936
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2b62ef008f1254b2b9f6e4ddbf09cd04, type: 3}
  m_Name: Noise_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &4109851466401226335
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 83b4d77f969318643be69a2c0738f616, type: 3}
  m_Name: PictureCorrection_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &4439151865633544354
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 77908a6207cd6654d8e9e2d41276e19b, type: 3}
  m_Name: TVEffect_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &4569238106607823226
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d52d4e1ac19514b08b776eb5dbdeaeae, type: 3}
  m_Name: LowRes_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &4580510352530173857
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e2f6178ec3d0143deb3c009eb674c298, type: 3}
  m_Name: Glitch3
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &4796484695405141665
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9f4e1311cf36142b58028111072a48c4, type: 3}
  m_Name: EdgeStretch_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &5256962531726100406
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6b4aaab42e3f7614ca4c47faae323b21, type: 3}
  m_Name: LimitlessVHSTapeRewind
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &5409837567437277680
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a148ea31cb6531d418dcdae4741fe8d2, type: 3}
  m_Name: OldFilm2_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &6161772826145683456
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4011361878b5c4401a4b2998341a4dbc, type: 3}
  m_Name: OldFilm_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &6391833273432820578
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0c4b56c4489e7452c9b8a62543a66430, type: 3}
  m_Name: AnalogTVNoise_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &7710238506235992648
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 679166022f5a14a1a8286a4babc39c9a, type: 3}
  m_Name: ColormapPalette_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
--- !u!114 &8158104216630410722
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2f989841c4a594e2e89a59ea3916bbbd, type: 3}
  m_Name: Negative_RLPRO
  m_EditorClassIdentifier: 
  m_Active: 1
  Event: 550
